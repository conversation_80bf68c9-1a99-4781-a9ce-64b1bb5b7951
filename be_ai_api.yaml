openapi: 3.0.3
info:
  title: entanglement-dev-be-ai-rest-api
  description: API Gateway for AI and Machine Learning Services
  version: 1.0.0

components:
  securitySchemes:
    entanglement-dev-be-ai-cognito-authorizer:
      type: apiKey
      name: Authorization
      in: header
      x-amazon-apigateway-authtype: cognito_user_pools
      x-amazon-apigateway-authorizer:
        type: cognito_user_pools
        providerARNs:
          - arn:aws:cognito-idp:us-east-1:314146329201:userpool/us-east-1_zeQZWhZSp
        identitySource: method.request.header.Authorization
        authorizerResultTtlInSeconds: 300
paths:
  /ai/models:
    get:
      summary: List Available AI Models
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: GET
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/models
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid request parameters"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "Resource not found"}'
      responses:
        '200':
          description: Successfully retrieved available AI models
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '500':
          description: Internal Server Error

  /ai/inference:
    post:
      summary: Run AI Inference
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/inference
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid request body or parameters"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "429":
            statusCode: "429"
            responseTemplates:
              application/json: '{"error": "Too Many Requests", "message": "Rate limit exceeded"}'
      responses:
        '200':
          description: Inference completed successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '429':
          description: Too Many Requests
        '500':
          description: Internal Server Error

  /ai/training:
    post:
      summary: Start Model Training
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/training
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "202":
            statusCode: "202"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid training parameters"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "409":
            statusCode: "409"
            responseTemplates:
              application/json: '{"error": "Conflict", "message": "Training job already in progress"}'
      responses:
        '202':
          description: Training job accepted and started
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '409':
          description: Conflict
        '500':
          description: Internal Server Error

  /ai/training/{job_id}:
    get:
      summary: Get Training Job Status
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      parameters:
        - name: job_id
          in: path
          required: true
          schema:
            type: string
      x-amazon-apigateway-integration:
        type: http
        httpMethod: GET
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/training/{job_id}
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid job ID format"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "Training job not found"}'
      responses:
        '200':
          description: Training job status retrieved successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '500':
          description: Internal Server Error

  /ai/embeddings:
    post:
      summary: Generate Text Embeddings
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/embeddings
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid text input"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "413":
            statusCode: "413"
            responseTemplates:
              application/json: '{"error": "Payload Too Large", "message": "Text input exceeds maximum size"}'
      responses:
        '200':
          description: Embeddings generated successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '413':
          description: Payload Too Large
        '500':
          description: Internal Server Error

  /ai/classification:
    post:
      summary: Text Classification
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/classification
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid text for classification"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "422":
            statusCode: "422"
            responseTemplates:
              application/json: '{"error": "Unprocessable Entity", "message": "Text cannot be classified"}'
      responses:
        '200':
          description: Text classification completed successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '422':
          description: Unprocessable Entity
        '500':
          description: Internal Server Error
  /ai/sentiment:
    post:
      summary: Sentiment Analysis
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/sentiment
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid text for sentiment analysis"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "422":
            statusCode: "422"
            responseTemplates:
              application/json: '{"error": "Unprocessable Entity", "message": "Text sentiment cannot be analyzed"}'
      responses:
        '200':
          description: Sentiment analysis completed successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '422':
          description: Unprocessable Entity
        '500':
          description: Internal Server Error

  /ai/recommendation:
    post:
      summary: Get AI Recommendations
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      parameters:
        - name: user_id
          in: query
          required: true
          schema:
            type: string
        - name: limit
          in: query
          required: false
          schema:
            type: integer
            default: 10
      x-amazon-apigateway-integration:
        type: http
        httpMethod: POST
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/recommendation
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid user ID or parameters"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "User not found"}'
      responses:
        '200':
          description: Recommendations generated successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '500':
          description: Internal Server Error

  /ai/health:
    get:
      summary: AI Service Health Check
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: GET
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/ai/health
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "503":
            statusCode: "503"
            responseTemplates:
              application/json: '{"error": "Service Unavailable", "message": "AI service is currently unavailable"}'
      responses:
        '200':
          description: AI service is healthy
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '503':
          description: Service Unavailable
        '500':
          description: Internal Server Error

  /:
    get:
      summary: AI API Docs Redirect
      security:
        - entanglement-dev-be-ai-cognito-authorizer: []
      x-amazon-apigateway-integration:
        type: http
        httpMethod: GET
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-be-ai-nlb-dev-f2875a6e43eb353e.elb.us-east-1.amazonaws.com:4999/
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "302":
            statusCode: "302"
            responseTemplates:
              application/json: ""
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
      responses:
        '302':
          description: Redirect to API documentation
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '500':
          description: Internal Server Error