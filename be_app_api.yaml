openapi: 3.0.3
info:
  title: entanglement-dev-be-app-rest-api
  description: API Gateway for User and Recipe Management
  version: 1.0.0

components:
  securitySchemes:
    entanglement-dev-be-app-cognito-authorizer:
      type: apiKey
      name: Authorization
      in: header
      x-amazon-apigateway-authtype: cognito_user_pools
      x-amazon-apigateway-authorizer:
        type: cognito_user_pools
        providerARNs:
          - arn:aws:cognito-idp:us-east-1:314146329201:userpool/us-east-1_zeQZWhZSp
        identitySource: method.request.header.Authorization
        authorizerResultTtlInSeconds: 300

paths:
  /me:
    get:
      summary: Get Current User Info
      security:
        - entanglement-dev-be-app-cognito-authorizer: []
      parameters:
        - name: user_id
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: User information retrieved successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      x-amazon-apigateway-integration:
        type: http
        httpMethod: GET
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-dev-be-app-use1-44feacb0ab337f75.elb.us-east-1.amazonaws.com:7005/me
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid user ID parameter"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "User not found"}'
    put:
      summary: Update Account
      security:
        - entanglement-dev-be-app-cognito-authorizer: []
      responses:
        '200':
          description: Account updated successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '422':
          description: Unprocessable Entity
        '500':
          description: Internal Server Error
      x-amazon-apigateway-integration:
        type: http
        httpMethod: PUT
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-dev-be-app-use1-44feacb0ab337f75.elb.us-east-1.amazonaws.com:7005/me
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "200":
            statusCode: "200"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid request body or parameters"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "User not found"}'
          "422":
            statusCode: "422"
            responseTemplates:
              application/json: '{"error": "Unprocessable Entity", "message": "Validation failed"}'
    delete:
      summary: Delete Account
      security:
        - entanglement-dev-be-app-cognito-authorizer: []
      parameters:
        - name: user_id
          in: query
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Account deleted successfully
        '400':
          description: Bad Request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '409':
          description: Conflict
        '500':
          description: Internal Server Error
      x-amazon-apigateway-integration:
        type: http
        httpMethod: DELETE
        connectionType: VPC_LINK
        connectionId: ccfr0p
        uri: http://entanglement-dev-be-app-use1-44feacb0ab337f75.elb.us-east-1.amazonaws.com:7005/me
        timeoutInMillis: 29000
        responses:
          default:
            statusCode: "500"
            responseTemplates:
              application/json: '{"error": "Internal Server Error", "message": "An unexpected error occurred"}'
          "204":
            statusCode: "204"
            responseTemplates:
              application/json: ""
          "400":
            statusCode: "400"
            responseTemplates:
              application/json: '{"error": "Bad Request", "message": "Invalid user ID parameter"}'
          "401":
            statusCode: "401"
            responseTemplates:
              application/json: '{"error": "Unauthorized", "message": "Authentication required"}'
          "403":
            statusCode: "403"
            responseTemplates:
              application/json: '{"error": "Forbidden", "message": "Access denied"}'
          "404":
            statusCode: "404"
            responseTemplates:
              application/json: '{"error": "Not Found", "message": "User not found"}'
          "409":
            statusCode: "409"
            responseTemplates:
              application/json: '{"error": "Conflict", "message": "Account cannot be deleted due to existing dependencies"}'
